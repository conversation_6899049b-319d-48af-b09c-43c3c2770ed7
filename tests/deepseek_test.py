# Copyright 2025 Google LLC.
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

"""Tests for the DeepSeek provider."""

import os
from unittest import mock

from absl.testing import absltest

from langextract import data
from langextract import exceptions
from langextract import factory
from langextract import inference
from langextract.providers import registry


class DeepSeekProviderTest(absltest.TestCase):
  """Tests for DeepSeek provider functionality."""

  def setUp(self):
    super().setUp()
    # Clear registry to ensure clean state
    registry.clear()

  def tearDown(self):
    super().tearDown()
    # Clear registry after each test
    registry.clear()

  @mock.patch('openai.OpenAI')
  def test_deepseek_provider_initialization(self, mock_openai):
    """Test that DeepSeek provider initializes correctly."""
    # Import here to trigger registration
    from langextract.providers import deepseek  # pylint: disable=import-outside-toplevel

    provider = deepseek.DeepSeekLanguageModel(
        model_id='deepseek-chat',
        api_key='test-key'
    )

    self.assertEqual(provider.model_id, 'deepseek-chat')
    self.assertEqual(provider.api_key, 'test-key')
    self.assertEqual(provider.base_url, 'https://api.deepseek.com')
    self.assertEqual(provider.format_type, data.FormatType.JSON)
    self.assertEqual(provider.temperature, 0.0)
    self.assertEqual(provider.max_workers, 10)

    # Verify OpenAI client was initialized with correct parameters
    mock_openai.assert_called_once_with(
        api_key='test-key',
        base_url='https://api.deepseek.com'
    )

  def test_deepseek_provider_requires_api_key(self):
    """Test that DeepSeek provider requires an API key."""
    from langextract.providers import deepseek  # pylint: disable=import-outside-toplevel

    with self.assertRaises(exceptions.InferenceConfigError) as cm:
      deepseek.DeepSeekLanguageModel(model_id='deepseek-chat')

    self.assertIn('API key not provided for DeepSeek', str(cm.exception))

  def test_deepseek_provider_requires_openai_package(self):
    """Test that DeepSeek provider requires openai package."""
    with mock.patch.dict('sys.modules', {'openai': None}):
      with self.assertRaises(exceptions.InferenceConfigError) as cm:
        from langextract.providers import deepseek  # pylint: disable=import-outside-toplevel
        deepseek.DeepSeekLanguageModel(model_id='deepseek-chat', api_key='test')

      self.assertIn('DeepSeek provider requires openai package', str(cm.exception))

  def test_deepseek_provider_registry_patterns(self):
    """Test that DeepSeek provider is registered with correct patterns."""
    from langextract.providers import deepseek  # pylint: disable=import-outside-toplevel

    # Test model ID patterns
    test_cases = [
        'deepseek-chat',
        'deepseek-reasoner',
        'deepseek-v3',
        'deepseek-coder',
    ]

    for model_id in test_cases:
      provider_class = registry.resolve(model_id)
      self.assertEqual(provider_class, deepseek.DeepSeekLanguageModel)

  def test_deepseek_provider_via_factory(self):
    """Test creating DeepSeek provider via factory."""
    from langextract.providers import deepseek  # pylint: disable=import-outside-toplevel

    config = factory.ModelConfig(
        model_id='deepseek-chat',
        provider_kwargs={'api_key': 'test-key'}
    )

    with mock.patch('openai.OpenAI'):
      model = factory.create_model(config)

    self.assertIsInstance(model, deepseek.DeepSeekLanguageModel)
    self.assertEqual(model.model_id, 'deepseek-chat')
    self.assertEqual(model.api_key, 'test-key')

  @mock.patch.dict(os.environ, {"DEEPSEEK_API_KEY": "env-deepseek-key"})
  def test_uses_deepseek_api_key_from_environment(self):
    """Factory should use DEEPSEEK_API_KEY from environment for DeepSeek models."""
    from langextract.providers import deepseek  # pylint: disable=import-outside-toplevel

    config = factory.ModelConfig(model_id="deepseek-chat")

    with mock.patch('openai.OpenAI'):
      model = factory.create_model(config)

    self.assertEqual(model.api_key, "env-deepseek-key")

  @mock.patch.dict(
      os.environ, {"LANGEXTRACT_API_KEY": "env-langextract-key"}, clear=True
  )
  def test_falls_back_to_langextract_api_key_when_deepseek_key_missing(self):
    """Factory uses LANGEXTRACT_API_KEY when DEEPSEEK_API_KEY is missing."""
    from langextract.providers import deepseek  # pylint: disable=import-outside-toplevel

    config = factory.ModelConfig(model_id="deepseek-chat")

    with mock.patch('openai.OpenAI'):
      model = factory.create_model(config)

    self.assertEqual(model.api_key, "env-langextract-key")

  @mock.patch.dict(
      os.environ,
      {
          "DEEPSEEK_API_KEY": "deepseek-key",
          "LANGEXTRACT_API_KEY": "langextract-key",
      },
  )
  def test_deepseek_specific_key_takes_priority_over_langextract_key(self):
    """Factory prefers DEEPSEEK_API_KEY over LANGEXTRACT_API_KEY."""
    from langextract.providers import deepseek  # pylint: disable=import-outside-toplevel

    config = factory.ModelConfig(model_id="deepseek-chat")

    with mock.patch('openai.OpenAI'):
      model = factory.create_model(config)

    self.assertEqual(model.api_key, "deepseek-key")

  @mock.patch('openai.OpenAI')
  def test_deepseek_custom_base_url(self, mock_openai):
    """Test DeepSeek provider with custom base URL."""
    from langextract.providers import deepseek  # pylint: disable=import-outside-toplevel

    custom_url = 'https://custom.deepseek.com'
    provider = deepseek.DeepSeekLanguageModel(
        model_id='deepseek-chat',
        api_key='test-key',
        base_url=custom_url
    )

    self.assertEqual(provider.base_url, custom_url)
    mock_openai.assert_called_once_with(
        api_key='test-key',
        base_url=custom_url
    )

  @mock.patch('openai.OpenAI')
  def test_deepseek_yaml_format(self, mock_openai):
    """Test DeepSeek provider with YAML format."""
    from langextract.providers import deepseek  # pylint: disable=import-outside-toplevel

    provider = deepseek.DeepSeekLanguageModel(
        model_id='deepseek-chat',
        api_key='test-key',
        format_type=data.FormatType.YAML
    )

    self.assertEqual(provider.format_type, data.FormatType.YAML)

  def test_explicit_provider_selection(self):
    """Test explicit DeepSeek provider selection."""
    from langextract.providers import deepseek  # pylint: disable=import-outside-toplevel

    config = factory.ModelConfig(
        model_id='some-model',
        provider='DeepSeekLanguageModel',
        provider_kwargs={'api_key': 'test-key'}
    )

    with mock.patch('openai.OpenAI'):
      model = factory.create_model(config)

    self.assertIsInstance(model, deepseek.DeepSeekLanguageModel)


if __name__ == '__main__':
  absltest.main()
